import { useAtom, useAtomValue } from 'jotai'
import {
  mazeResultImagesAtom,
  screenOrientationAtom,
  selectedGenderAtom,
} from '@/stores'
import { useSearchParams } from 'react-router-dom'
import { useMemo, useEffect } from 'react'
import classNames from 'classnames'

import { CreateBtn } from '../homepage/CreateBtn'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import useSWR from 'swr'
import { MirrorLoading } from 'wujieai-react-icon'
import { MyImage } from '@/components/ui/MyImage'
import { useTranslation } from 'react-i18next'

export const MazeThemeDetail = () => {
  const [, setResultImages] = useAtom(mazeResultImagesAtom)
  const [screenOrientation] = useAtom(screenOrientationAtom)
  const activeGender = useAtomValue(selectedGenderAtom)
  const { t } = useTranslation()

  const [searchParams] = useSearchParams()
  const themeId = searchParams.get('themeId')

  // 获取主题详情数据
  const { data: themeDetailData, isLoading } = useSWR(
    themeId ? [themeId] : null,
    ([id]) => _ajax.get(_api.theme_detail, { params: { id } })
  )

  const themeDetail = useMemo(() => {
    return themeDetailData?.data?.data
  }, [themeDetailData])

  const detailList = useMemo(() => {
    return themeDetail?.itemList || []
  }, [themeDetail])

  useEffect(() => {
    return () => {
      setResultImages({})
    }
  }, [setResultImages])

  console.log('主题详情数据:', themeDetail)
  console.log('模板列表:', detailList)

  return (
    <>
      <div className="flex items-center justify-center h-full w-full flex-col">
        <h1 className="maze-page-title pb-6">
          {themeDetail?.name || t('主题详情')}
        </h1>

        {/* 加载状态 */}
        {isLoading && (
          <div className="flex items-center justify-center w-full h-[400px]">
            <MirrorLoading className="animate-spin maze-primary-text" />
          </div>
        )}

        {/* 主题详情内容 */}
        {!isLoading && themeDetail && (
          <div className="w-full">
            <h2 className="text-[2.625rem] text-center pb-10">
              {t('模板列表')} ({detailList?.length || 0})
            </h2>
            <div
              className={classNames(
                'w-full overflow-x-auto whitespace-nowrap leading-none text-[0]',
                screenOrientation.isLandScape
                  ? 'h-[400px]'
                  : 'h-[60vh] whitespace-pre-wrap overflow-y-auto ipad:h-[66dvh]'
              )}
            >
              {detailList && detailList?.length === 0 && (
                <div className="flex items-center justify-center w-full h-full">
                  <div className="text-center">
                    <div className="text-[2rem] leading-[2.5rem] font-bold text-ellipsis maze-primary-text opacity-65">
                      {t('暂无数据')}
                    </div>
                  </div>
                </div>
              )}
              {detailList?.map(
                (
                  item: { image: string | undefined; name: string },
                  index: number
                ) => (
                  <div
                    key={index}
                    className={classNames(
                      'inline-block rounded-sm overflow-hidden mr-4 last:mr-0 relative',
                      screenOrientation.isLandScape
                        ? 'w-[20%]'
                        : 'w-[31.5%] space-y-2 my-2 phone:w-[48.1%] phone:h-[200px]'
                    )}
                    style={{
                      aspectRatio: '0.66',
                    }}
                  >
                    <MyImage
                      src={item.image}
                      tag="v800"
                      className="w-full h-full"
                      imgClassName="object-cover inline-block"
                      isAppCache={false}
                    />
                    <h2 className="absolute bottom-0 left-0 right-0 h-16 mt-0 truncate text-center leading-[4rem] text-[1.3rem] font-semibold px-4 text-white maze-theme-title-bg">
                      {item.name}
                    </h2>
                  </div>
                )
              )}
            </div>
          </div>
        )}

        {/* 无数据状态 */}
        {!isLoading && !themeDetail && (
          <div className="flex items-center justify-center w-full h-[400px]">
            <div className="text-center">
              <div className="text-[2rem] leading-[2.5rem] font-bold text-ellipsis maze-primary-text opacity-65">
                {t('主题不存在或已删除')}
              </div>
            </div>
          </div>
        )}

        {/* 下一步按钮 */}
        <div className="pt-8">
          <CreateBtn activeTemplate={themeDetail} activeGender={activeGender} />
        </div>
      </div>
    </>
  )
}
