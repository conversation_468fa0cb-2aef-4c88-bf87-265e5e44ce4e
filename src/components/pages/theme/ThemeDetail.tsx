import classnames from 'classnames'
import { useAtom } from 'jotai'
import { mazeResultImagesAtom, screenOrientationAtom } from '@/stores'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useState, useMemo, useRef, useEffect } from 'react'
import { MirrorAiTaskStatus } from '@/graphqls/types'
import classNames from 'classnames'
import { Swiper as SwiperType } from 'swiper/types'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Pagination } from 'swiper/modules'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/grid'

import { SvgIcon } from '@/components/ui/SvgIcon'
import { isIPad, isPhone } from '@/utils'
import { MainImage } from '../result/MainImage'
import { CreateBtn } from '../homepage/CreateBtn'

const SwiperNextButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute right-6 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slideNext()}
    >
      <SvgIcon
        src="/images/icons/arrow-right.svg"
        alt="下一页"
        className="w-24 h-24"
      />
    </div>
  )
}

const SwiperPrevButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute left-6 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slidePrev()}
    >
      <SvgIcon
        src="/images/icons/arrow-left.svg"
        alt="上一页"
        className="w-24 h-24"
      />
    </div>
  )
}

export const MazeThemeDetail = () => {
  const swiperRef = useRef<SwiperType | null>(null)
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)
  const [activeSlideIndex, setActiveSlideIndex] = useState(0)
  const [resultImages, setResultImages] = useAtom(mazeResultImagesAtom)
  const [screenOrientation] = useAtom(screenOrientationAtom)

  const [searchParams] = useSearchParams()

  useEffect(() => {
    return () => {
      setResultImages({})
    }
  }, [])

  useEffect(() => {
    const swiper = swiperRef.current
    if (swiper) {
      // Update navigation state on slide change
      swiper.on('slideChange', () => {
        setIsBeginning(swiper.isBeginning)
        setIsEnd(swiper.isEnd)
        setActiveSlideIndex(swiper.activeIndex)
      })

      // Initialize states
      setIsBeginning(swiper.isBeginning)
      setIsEnd(swiper.isEnd)
      setActiveSlideIndex(swiper.activeIndex)
    }
  }, [swiperRef.current])

  const taskBaseIds = searchParams.get('taskBaseId')?.split(',')
  console.log('taskBaseId', taskBaseIds)

  const multiline = screenOrientation.isPortrait
  // Default Swiper parameters
  const defaultSwiperProps = useMemo(
    () => ({
      // There are many special requirements and they are not universal. Make modifications with caution
      slidesPerView: multiline ? 1 : 4, // 横屏则展示4个
      spaceBetween: isIPad() ? 32 : isPhone() ? 32 : 40,
      loop: false,
      modules: [Pagination],
      className: 'mySwiper',
      initialSlide: 0,
    }),
    [multiline, taskBaseIds?.length, screenOrientation.isPortrait]
  )

  return (
    <>
      <div className="flex items-center justify-center h-full w-full flex-col">
        <h1 className="maze-page-title pb-6">Theme Title</h1>
        <div
          className={classNames(
            'py-10 w-[82vw] h-auto overflow-hidden',
            screenOrientation.isLandScape
              ? ''
              : 'w-[82vw] px-20 h-auto ipad:w-[72vw] ipad:py-2 ipad:h-auto phone:h-auto phone:py-2 phone:px-0'
          )}
        >
          {taskBaseIds && taskBaseIds.length >= 2 && (
            <>
              <div
                className={classNames({
                  'hidden pointer-events-none': isBeginning,
                })}
              >
                <SwiperPrevButton swiperRef={swiperRef} />
              </div>
              <div
                className={classNames({
                  'hidden pointer-events-none': isEnd,
                })}
              >
                <SwiperNextButton swiperRef={swiperRef} />
              </div>
            </>
          )}
          <Swiper
            {...defaultSwiperProps}
            onSwiper={(swiper: any) => {
              swiperRef.current = swiper
            }}
          >
            {taskBaseIds?.map((taskBaseId, index) => {
              return (
                <SwiperSlide
                  key={index}
                  className={classnames([
                    'cursor-pointer self-stretch transition-all rounded-2xl relative bg-black flex-shrink-0 overflow-hidden',
                  ])}
                >
                  <MainImage />
                </SwiperSlide>
              )
            })}
          </Swiper>
        </div>
        {/* 缩略图导航 */}
        {taskBaseIds && taskBaseIds.length > 1 && multiline && (
          <div className="mt-6 w-full flex justify-center px-8">
            <div
              className="flex gap-3 p-3 rounded-2xl max-w-full overflow-x-auto scrollbar-hide"
              style={{ scrollBehavior: 'smooth' }}
            >
              {taskBaseIds.map((taskBaseId, index) => {
                const taskImages = resultImages[Number(taskBaseId)] || []
                const isActive = index === activeSlideIndex

                return (
                  <div
                    key={index}
                    onClick={() => {
                      swiperRef.current?.slideTo(index)
                    }}
                    className={classNames(
                      'relative cursor-pointer rounded-2xl overflow-hidden transition-all duration-300 flex-shrink-0',
                      'w-32',
                      isActive
                        ? 'ring-1 ring-black ring-opacity-70'
                        : 'opacity-70 hover:opacity-90'
                    )}
                    style={{ aspectRatio: '0.66' }} // 高度自适应，保持0.66的宽高比
                  >
                    {taskImages.length > 0 &&
                    taskImages[0].status === MirrorAiTaskStatus.SUCCESS ? (
                      <>
                        <img
                          src={
                            taskImages[0].previewUrl ||
                            taskImages[0].resultUrl ||
                            ''
                          }
                          alt={`${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </>
                    ) : (
                      <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                        <div className="text-xs text-gray-400 animate-pulse">
                          ...
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        )}
        <div className="pt-8">
          <CreateBtn />
        </div>
      </div>
    </>
  )
}
